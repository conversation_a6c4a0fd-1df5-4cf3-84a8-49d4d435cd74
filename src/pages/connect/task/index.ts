import {CONFIG_TYPE, PAGER_SUI, ROUTE_PATH, TABLE_SUI} from '@/common/config';
import {decorators, html, redirect} from '@baiducloud/runtime';
import {
    Button,
    Search,
    Table,
    Pagination,
    Dropdown,
    Menu,
    Tooltip,
    Drawer,
} from '@baidu/sui';
import {AppListPage} from '@baidu/sui-biz';
import InstantEditor from '@/components/instant-editor';
import ListTitle from '@/components/list-title';
import CommonTable from '@/components/common-table';
import {getTimer} from '@/common/util';
import {columns, ToggleTableColumn} from './config';
import './index.less';

const klass = 'kafka-connect-task';

let timeCount: number = 0;
@decorators.asPage(ROUTE_PATH.connectTask)
@decorators.withSidebar({active: ROUTE_PATH.connectTask})
export default class ConnectTask extends CommonTable {
    static template = html`
    <div class="${klass} kafka-connect-task">
        <app-list-page class="${klass}_content">
            <div slot="pageTitle" class="${klass}_content-header">
                <list-title
                    title="Kafka Connect 任务"
                    type="${CONFIG_TYPE.VIP}"
                    hasOnlineAlert
                />
            </div>
            <div slot="bulk"></div>
            <div slot="filter" class="task-filter">
                <span class="task-name">任务名称</span>
                <s-search
                    class="task-search-box"
                    placeholder="{{searchbox.placeholder}}"
                    value="{= searchbox.keyword =}"
                    on-search="onSearch"
                    width="170"
                />
                <s-button
                    width="74"
                    height="32"
                    skin="primary"
                    on-click="onCreate">
                    新建任务
                </s-button>
            </div>
            <s-table
                columns="{{table.columns}}"
                loading="{{table.loading}}"
                error="{{table.error}}"
                datasource="{{table.datasource}}"
                on-sort="onSort"
                on-filter="onFilter">
                <div slot="c-name" class="${klass}__instant">
                    <div>
                        <a href="#${ROUTE_PATH.connectDetailInfo}?name={{row.name}}"
                            class="a-btn">
                            {{row.name}}
                        </a>
                        <instant-editor
                            value="{{row.name}}"
                            info="{{rowIndex}}"
                            disabled="{{row.status !== 'ACTIVE'}}"
                            request="{{editName}}"
                            check="{{check}}"
                            placeholder="请输入集群名称"
                            desc="不超过65个字符，仅支持数字/字母/特殊符号(_/-.)"
                        />
                    </div>
                </div>
                <div slot="c-operation">
                    <s-button
                        skin="stringfy"
                        class="table-btn-slim"
                        on-click="onPause(raw)">
                        暂停
                    </s-button>
                    <s-button
                        skin="stringfy"
                        class="table-btn-slim"
                        on-click="onStop(raw)">
                        停止
                    </s-button>
                    <s-button
                        skin="stringfy"
                        class="table-btn-slim"
                        on-click="onDelete(raw)">
                        删除
                    </s-button>
                    <s-dropdown class="operation-dropdown">
                        <s-menu slot="overlay">
                            <s-menu-item  s-for="operation,index in moreOperationData" key="{{index}}">
                                <s-tooltip content="{{operation | tipText(row)}}" placement="left">
                                    <s-button
                                        on-click="handleMenuItemClick(row, operation)"
                                        skin="stringfy"
                                        style="padding: 0;"
                                    >
                                        {{operation}}
                                    </s-button>
                                </s-tooltip>
                            </s-menu-item>
                        </s-menu>
                        <s-button skin="normal-stringfy">更多 <s-icon-down /></s-button>
                    </s-dropdown>
                </div>
            </s-table>
            <s-pagination
                slot="pager"
                s-if="{{!(!table.datasource.length && pager.page === 1)}}"
                layout="{{'total, pageSize, pager, go'}}"
                total="{{pager.count}}"
                pageSize="{{pager.pageSize}}"
                page="{{pager.page}}"
                pageSizes="{{pager.pageSizes}}"
                max-item="{{7}}"
                on-pagerChange="onPageChange"
                on-pagerSizeChange="onPageSizeChange"
            />
        </app-list-page>
        <!--修改规则抽屉页-->
        <s-drawer
            open="{=changeRules=}"
            title="数据处理"
            class="drawer"
            direction="right"
        >
            <div>This is drawer content</div>
        </s-drawer>
    </div>`;

    initData() {
        return {
            table: {
                ...TABLE_SUI,
                loading: true,
                columns: columns,
                datasource: // TODO:假数据要删除
                [
                    {
                        name: 'foo',
                        status: 'ACTIVE',
                        sourceCluster: '湖南/长沙',
                        targetCluster: 'clustername',
                        createTime: '2024-10-23 23:10:11',
                    },
                    {
                        name: 'bar',
                        status: '已停止',
                        sourceCluster: '北京/北京',
                        targetCluster: 'clustername',
                        createTime: '2024-10-23 23:10:11',
                    },
                    {
                        name: 'xxx',
                        status: '异常',
                        sourceCluster: '河北/石家庄',
                        targetCluster: 'clustername',
                        createTime: '2024-10-23 23:10:11',
                    }
                ],
            },
            moreOperationData: ['启动', '恢复', '修改规则'],
            pager: {...PAGER_SUI},
            editName: this.editName.bind(this),
            check: this.check.bind(this),
            changeRules: false,
        };
    }

    static components = {
        'app-list-page': AppListPage,
        'list-title': ListTitle,
        's-search': Search,
        's-table': Table,
        's-button': Button,
        's-pagination': Pagination,
        'instant-editor': InstantEditor,
        's-dropdown': Dropdown,
        's-menu': Menu,
        's-menu-item': Menu.Item,
        's-tooltip': Tooltip,
        's-drawer': Drawer,
    };


    async attached() {
        await this.getComList();
        timeCount = getTimer(() => this.getComList(false));
    }

    onRefresh() {
        this.getComList();
        clearInterval(timeCount);
        timeCount = getTimer(() => this.getComList(false));
    }

    detached() {
        clearInterval(timeCount);
    }

    /**
     * 获取表格数据的通用方法,支持自动刷新时不展示loading状态
     */
    async getComList(loadStatus = true) {
        // 初始化表格状态：设置loading状态和清除错误状态
        this.data.set('table.loading', loadStatus);
        this.data.set('table.error', false);

        try {
            // 调用具体的数据获取方法
            await this.getTableList();
            // 数据获取成功，关闭loading状态
            this.data.set('table.loading', false);
        }
        catch (error) {
            // 数据获取失败，设置错误状态并关闭loading
            this.data.set('table.error', true);
            this.data.set('table.loading', false);
        }
    }

    /**
     * 获取表格数据 - 实现 CommonTable 的抽象方法
     * TODO: 当 Kafka Connect 任务 API 可用时，需要替换为实际的 API 调用
     */
    async getTableList() {
        // 暂时设置空数据，等待 API 实现
        // const {searchbox, pager} = this.data.get('');
        // const params = {
        //     pageNo: pager.page,
        //     pageSize: pager.pageSize,
        //     keyword: searchbox.keyword,
        //     keywordType: searchbox.keywordType[0]
        // };
        // const {totalCount, result} = await api.getConnectTaskList(params);
        // this.data.set('pager.count', totalCount);
        // this.data.set('table.datasource', result);

        // 临时实现：设置空数据
        this.data.set('pager.count', 0);
        // this.data.set('table.datasource', []);
    }

    /**
     * 创建任务，重定向到创建界面
     */
    async onCreate() {
        redirect(`#${ROUTE_PATH.connectCreate}`);
    }

    /**
     * 编辑连接器任务名称
     */
    async editName() {
        // TODO
    }

    /**
     * 连接器任务暂停
     */
    async onPause(raw: ToggleTableColumn) {
        // TODO
        return raw;
    }

    /**
     * 连接器任务停止
     */
    async onStop(raw: ToggleTableColumn) {
        // TODO
        return raw;
    }

    /**
     * 连接器任务删除
     */
    async onDelete(raw: ToggleTableColumn) {
        // TODO
        return raw;
    }

    /**
     * 连接器任务启动
     */
    async onStart(raw: ToggleTableColumn) {
        // TODO
        return raw;
    }

    /**
     * 连接器任务恢复
     */
    async onRestart(raw: ToggleTableColumn) {
        // TODO
        return raw;
    }

    /**
     * 任务搜索，支持搜索后自动刷新定时器重置
     */
    onSearch(): void {
        // 重置分页
        this.data.set('pager.page', 1);
        // 刷新数据
        this.onRefresh();
    }

    /**
     * 名称输入校验
     */
    check(name: string, callback: (str: string) => void) {
        if (!name) {
            return callback('请输入');
        }
        if (name.length > 65) {
            return callback('不能超过65个字符');
        }
        if (!/^[a-zA-Z()_\/\-.\d]{1,}$/.test(name)) {
            return callback('输入字符格式有误');
        }
        callback('');
    }

    /**
     * 更多操作处理
     */
    handleMenuItemClick(row: ToggleTableColumn, operation: string) {
        switch (operation) {
            case '修改规则':
                // 规则变更
                this.data.set('changeRules', true);
                break;
            case '恢复':
                this.onRestart(row);
                break;
            case '启动':
                this.onStart(row);
                break;
        }
    }

    /**
     * 分页处理
     */
    onPageChange(args: {value: {page: number, pageSize: number}}) {
        this.data.set('pager.pageSize', args.value.pageSize);
        this.data.set('pager.page', args.value.page);
        this.onRefresh();
    }

    /**
     * 分页页面显示条数更改处理
     */
    onPageSizeChange(args: {value: {page: number, pageSize: number}}) {
        this.data.set('pager.pageSize', args.value.pageSize);
        this.data.set('pager.page', 1);
        this.onRefresh();
    }
}
