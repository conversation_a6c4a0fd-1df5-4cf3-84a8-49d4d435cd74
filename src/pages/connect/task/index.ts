import {CONFIG_TYPE, ROUTE_PATH, TABLE_SUI} from '@/common/config';
import {decorators, html} from '@baiducloud/runtime';
import {
    Button,
    Search,
    Table,
    Pagination,
} from '@baidu/sui';
import {AppListPage} from '@baidu/sui-biz';

import ListTitle from '@/components/list-title';
import CommonTable from '@/components/common-table';
import {getTimer} from '@/common/util';
import {columns} from './config';
import './index.less';

const klass = 'kafka-connect-task';

let timeCount: number = 0;
@decorators.asPage(ROUTE_PATH.connectTask)
@decorators.withSidebar({active: ROUTE_PATH.connectTask})
export default class ConnectTask extends CommonTable {
    static template = html`
    <div class="${klass} kafka-connect-task">
        <app-list-page class="${klass}_content">
            <div slot="pageTitle" class="${klass}_content-header">
                <list-title
                    title="Kafka Connect 任务"
                    type="${CONFIG_TYPE.VIP}"
                    hasOnlineAlert
                />
            </div>
            <div slot="bulk"></div>
            <div slot="filter" class="task-filter">
                <span class="task-name">任务名称</span>
                <s-search
                    class="task-search-box"
                    placeholder="{{searchbox.placeholder}}"
                    value="{= searchbox.keyword =}"
                    on-search="onSearch"
                    width="170"
                />
                <s-button
                    width="74"
                    height="32"
                    skin="primary"
                    on-click="onClickHandle">
                    新建任务
                </s-button>
            </div>
            <s-table
                columns="{{table.columns}}"
                loading="{{table.loading}}"
                error="{{table.error}}"
                datasource="{{table.datasource}}"
                on-sort="onSort"
                on-filter="onFilter">
                <div slot="c-operation">
                    <s-button
                        skin="stringfy"
                        class="table-btn-slim"
                        on-click="onIpGet(row)">
                        暂停
                    </s-button>
                    <s-button
                        skin="stringfy"
                        class="table-btn-slim"
                        on-click="onIpGet(row)">
                        停止
                    </s-button>
                    <s-button
                        skin="stringfy"
                        class="table-btn-slim"
                        on-click="onIpGet(row)">
                        删除
                    </s-button>
                </div>
            </s-table>
            <s-pagination
                slot="pager"
                s-if="{{!(!table.datasource.length && pager.page === 1)}}"
                layout="{{'total, pageSize, pager, go'}}"
                total="{{pager.count}}"
                pageSize="{{pager.pageSize}}"
                page="{{pager.page}}"
                pageSizes="{{pager.pageSizes}}"
                max-item="{{7}}"
                on-pagerChange="onPageChange"
                on-pagerSizeChange="onPageSizeChange"
            />
        </app-list-page>
    </div>`;

    initData() {
        return {
            table: {
                ...TABLE_SUI,
                loading: true,
                columns: columns,
                datasource:
                [
                    {
                        name: 'foo',
                        status: '运行中',
                        sourceCluster: '湖南/长沙',
                        targetCluster: 'clustername',
                        createTime: '2024-10-23 23:10:11',
                        actions: ['暂停', '停止', '删除', '启动', '恢复', '修改规则']
                    },
                    {
                        name: 'bar',
                        status: '已停止',
                        sourceCluster: '北京/北京',
                        targetCluster: 'clustername',
                        createTime: '2024-10-23 23:10:11',
                        actions: ['暂停', '停止', '删除', '启动', '恢复', '修改规则']
                    },
                    {
                        name: 'xxx',
                        status: '异常',
                        sourceCluster: '河北/石家庄',
                        targetCluster: 'clustername',
                        createTime: '2024-10-23 23:10:11',
                        actions: ['暂停', '停止', '删除', '启动', '恢复', '修改规则']
                    }
                ],
            },
        };
    }

    static components = {
        'app-list-page': AppListPage,
        'list-title': ListTitle,
        's-search': Search,
        's-table': Table,
        's-button': Button,
        's-pagination': Pagination,
    };


    async attached() {
        await this.getComList();
        timeCount = getTimer(() => this.getComList(false));
    }

    onRefresh() {
        this.getComList();
        clearInterval(timeCount);
        timeCount = getTimer(() => this.getComList(false));
    }

    detached() {
        clearInterval(timeCount);
    }

    /**
     * 获取表格数据的通用方法,支持自动刷新时不展示loading状态
     */
    async getComList(loadStatus = true) {
        // 初始化表格状态：设置loading状态和清除错误状态
        this.data.set('table.loading', loadStatus);
        this.data.set('table.error', false);

        try {
            // 调用具体的数据获取方法
            await this.getTableList();
            // 数据获取成功，关闭loading状态
            this.data.set('table.loading', false);
        }
        catch (error) {
            // 数据获取失败，设置错误状态并关闭loading
            this.data.set('table.error', true);
            this.data.set('table.loading', false);
        }
    }

    /**
     * 获取表格数据 - 实现 CommonTable 的抽象方法
     * TODO: 当 Kafka Connect 任务 API 可用时，需要替换为实际的 API 调用
     */
    async getTableList(): Promise<void> {
        // 暂时设置空数据，等待 API 实现
        // 未来实现示例：
        // const {searchbox, pager} = this.data.get('');
        // const params = {
        //     pageNo: pager.page,
        //     pageSize: pager.pageSize,
        //     keyword: searchbox.keyword,
        //     keywordType: searchbox.keywordType[0]
        // };
        // const {totalCount, result} = await api.getConnectTaskList(params);
        // this.data.set('pager.count', totalCount);
        // this.data.set('table.datasource', result);

        // 临时实现：设置空数据
        this.data.set('pager.count', 0);
        // this.data.set('table.datasource', []);
    }
}
