export interface ToggleTableColumn {
    name: string;
    label: string;
    filter?: {
        options: any[];
        value: any;
    };
    width?: number;
    fixed?: 'left' | 'right';
    sortable?: boolean;
    render?: (item: any, rowIndex?: number) => string;
    /** 控制自定义列展示时，该项是否禁用（即无法隐藏） */
    disabled?: boolean;
    /** 控制自定义列展示时，该项是否默认展示 */
    defaultShow?: boolean;
}
export const columns = [
    {
        name: 'name',
        label: '任务名称',
        fixed: 'left',
        width: 200
    },
    {
        name: 'status',
        label: '任务状态',
        width: 100
    },
    {
        name: 'sourceCluster',
        label: '源端集群',
        width: 100
    },
    {
        name: 'targetCluster',
        label: '目标集群',
        width: 100
    },
    {
        name: 'createTime',
        label: '创建时间',
        width: 100
    },
    {
        name: 'operation',
        label: '操作',
        width: 120,
    }
];
