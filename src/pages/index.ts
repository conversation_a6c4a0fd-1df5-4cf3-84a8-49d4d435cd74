/**
 * index
 * @file index
 * <AUTHOR>
 */
import {decorators, ServiceFactory} from '@baiducloud/runtime';
import ClusterList from './cluster/list';
import TopicList from './topic/list';

const $flag = ServiceFactory.resolve('$flag');

if ($flag.KafkaVip || $flag.isXushang) {
    decorators.asPage('/')(ClusterList);
}
else {
    decorators.asPage('/')(TopicList);
}

import './cluster/index';
import './cluster-config/index';
import './connect/index';
import './topic/monitor';

import './certificate/list';
import './consumer-group/list';
import './new-active';
import './vip-active';
