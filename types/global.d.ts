/**
 * @file global.d.ts
 * <AUTHOR>
 */

// san Component computed
declare interface SanComputedProps {
    [x: string]: (this: { data: Data }) => any;
}
// san Component filters
declare interface SanFilterProps<T = Data> {
    [k: string]: (this: { data: T }, ...filterOption: any[]) => any
}

declare interface SanMessageProps<T = Data> {
    [x: string]: (this: { data: T; fire: Function; [prop: string]: any }, e: any) => any;
}

// Object
declare interface Object {
    $context: any,
    $framework: any
}

// EnumItem
declare interface EnumItem {
    alias: string
    text: string
    value: string | number
    [key: string]: any
}

declare type NormalObject = {[x: string]: any}

// 订阅声明类型
declare interface Subscription {
    partition: string
    commitOffset: string
    logEndOffset: string
    lag: string
    consumerInstance: string
    commitTimestamp: number
};

// 账单相关的orderitem显示
interface OrderItemObj {
    title: string
    type: string
    isShowNetworkPrice?: boolean
    list: Array<{
        label: string
        text: string | number
        hasAddition?: boolean 
        addition?: Array<{label: string, text: string | number}>
    }>
}

// 需要有更新功能的页面
class DetailRefresh {
    abstract refreshInfo(arg?: Object): Promise<any> {}
}

// 报错信息返回类型
interface AlarmType {
    alarmStateCount: string
    disabledCount: string
    insufficientStateCount: string
    okStateCount: string
    totalCount: string
}
